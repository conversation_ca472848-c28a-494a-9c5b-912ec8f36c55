'use client'
import { motion } from 'motion/react';
import { ReactNode } from 'react';

import { SITE_CONFIG } from '@/app/constants';
import worksData from '../data/works-data.json';

import { <PERSON> } from './<PERSON>';
import { StructuredData, generateStructuredData } from './SEO';
import { WorkCarousel } from './WorkCarousel';

const VARIANTS_CONTAINER = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.15,
    },
  },
}

const VARIANTS_SECTION = {
  hidden: { opacity: 0, y: 20, filter: 'blur(8px)' },
  visible: { opacity: 1, y: 0, filter: 'blur(0px)' },
}

const TRANSITION_SECTION = {
  duration: 0.3,
}

// Props for the Personal component
interface PersonalClientProps {
  workSection: ReactNode
}

export function PersonalClient({ workSection }: PersonalClientProps) {
  // Generate structured data for the homepage
  const structuredData = generateStructuredData({
    type: 'website',
    title: SITE_CONFIG.title,
    description: SITE_CONFIG.description,
    url: SITE_CONFIG.url,
  })

  return (
    <>
      <StructuredData data={structuredData} />
      <motion.main
        className="space-y-24"
        variants={VARIANTS_CONTAINER}
        initial="hidden"
        animate="visible"
      >
      <motion.section
        variants={VARIANTS_SECTION}
        transition={TRANSITION_SECTION}
      >
        <Hero />
      </motion.section>

      <motion.section
        variants={VARIANTS_SECTION}
        transition={TRANSITION_SECTION}
        id="work"
      >
        {workSection}
      </motion.section>
      {/* More Work Slideshow Section */}
      <motion.section
        variants={VARIANTS_SECTION}
        transition={TRANSITION_SECTION}
        id="more-work"
      >
        <h2 className="mt-24 mb-8 text-2xl font-regular text-zinc-900 dark:text-zinc-50 text-center">More work</h2>
        <div className="homepage-wide-breakout">
          <WorkCarousel
            images={worksData
              .filter((work) => work.showOnHomepage !== true && !work.passwordLock)
              .map((work) => ({
                src: work.coverImage,
                alt: work.title,
                caption: work.subtitle || work.title,
              }))}
          />
        </div>
      </motion.section>
    </motion.main>
    </>
  )
}
